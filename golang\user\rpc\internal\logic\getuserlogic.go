package logic

import (
	"context"
	"fmt"

	"user/rpc/internal/svc"
	"user/rpc/user"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetUserLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserLogic {
	return &GetUserLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// GetUser retrieves user information by ID
func (l *GetUserLogic) GetUser(in *user.GetUserRequest) (*user.UserResponse, error) {

	// 根据id获取用户信息
	user,err:=l.svcCtx.UserModel.FindByID(in.Id)

	if err!=nil{
		return nil,fmt.Errorf("获取用户信息失败: %v", err)
	}

	// 

	return &user.UserResponse{}, nil
}
